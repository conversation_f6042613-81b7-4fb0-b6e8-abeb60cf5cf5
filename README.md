Dependencies:
```
pip install flask flask-sock python-dotenv openai deepgram-sdk requests flask_cors pandas chromadb sentence-transformers pyproj gtfs-realtime-bindings
```

Create .env file with the following keys:

```
DEEPGRAM_API_KEY=<your-deepgram-key>
OPENAI_API_KEY=<your-openai-key>
UTSA_ARCGIS_USERNAME=<your-utsa-arcgis-username> (Temporarily disabled till <PERSON> gets an account)
UTSA_ARCGIS_PASSWORD=<your-utsa-arcgis-password>
```

Get Deepgram key [here](https://developers.deepgram.com/docs/create-additional-api-keys).


Start server with:
```
python server.py
```


