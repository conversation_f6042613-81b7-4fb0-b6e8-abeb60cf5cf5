import requests
import google.protobuf
from google.transit import gtfs_realtime_pb2
import pandas as pd
from datetime import datetime
import time
import json


def parse_vehicle_positions(source, is_url=False):
    # Read the binary protobuf file
    feed = gtfs_realtime_pb2.FeedMessage()

    if is_url:
        response = requests.get(source)
        feed.ParseFromString(response.content)
    else:
        with open(source, 'rb') as f:
            feed.ParseFromString(f.read())

    # Print feed timestamp info
    feed_timestamp = feed.header.timestamp
    print(f"Feed timestamp: {feed_timestamp} (Unix epoch seconds)")
    print(f"Feed time: {datetime.fromtimestamp(feed_timestamp)}")

    # Extract vehicle position data
    vehicles_data = []

    for entity in feed.entity:
        if entity.HasField('vehicle'):
            vehicle = entity.vehicle

            # Create a dictionary for this vehicle
            vehicle_data = {
                'id': entity.id,
                'trip_id': vehicle.trip.trip_id if vehicle.HasField('trip') else None,
                'route_id': vehicle.trip.route_id if vehicle.HasField('trip') else None,
                'latitude': vehicle.position.latitude if vehicle.HasField('position') else None,
                'longitude': vehicle.position.longitude if vehicle.HasField('position') else None,
                'bearing': vehicle.position.bearing if vehicle.HasField('position') else None,
                'speed': vehicle.position.speed if vehicle.HasField('position') else None,
                'current_stop_sequence': vehicle.current_stop_sequence if vehicle.HasField(
                    'current_stop_sequence') else None,
                'current_status': vehicle.current_status if vehicle.HasField('current_status') else None,
                'timestamp': vehicle.timestamp if vehicle.HasField('timestamp') else None,
                'congestion_level': vehicle.congestion_level if vehicle.HasField('congestion_level') else None,
                'occupancy_status': vehicle.occupancy_status if vehicle.HasField('occupancy_status') else None
            }

            vehicles_data.append(vehicle_data)

    # Create a pandas DataFrame for easier viewing and analysis
    df = pd.DataFrame(vehicles_data)

    return df


def post_vehicle_data_to_server(vehicles_df, endpoint_url):
    """
    Post vehicle data to specified endpoint

    Parameters:
    vehicles_df (pandas.DataFrame): DataFrame containing vehicle data
    endpoint_url (str): URL to post data to

    Returns:
    list: List of response objects
    """
    responses = []
    print(f"Total {len(vehicles_df)} vehicles...")
    data = {
        "data": []
    }
    missingBuses = 0
    # Loop through each vehicle and post its data
    for index, vehicle in vehicles_df.iterrows():
        # Skip vehicles with missing data
        if pd.isna(vehicle['latitude']) or pd.isna(vehicle['longitude']) or pd.isna(vehicle['trip_id']):
            # print(f"Skipping vehicle {vehicle['id']} due to missing data")
            missingBuses += 1
            continue

        # Create the data payload

        data["data"].append({
            "lat": float(vehicle['latitude']),
            "lon": float(vehicle['longitude']),
            "trackid": str(vehicle['id']),
            "objectid": str(vehicle['id']) + str(int(vehicle['timestamp'])),
            "timestamp": int(vehicle['timestamp']),
            "route": str(vehicle['route_id'])
        })
    print(f"Will POST {len(vehicles_df) - missingBuses} vehicles to {endpoint_url}...")
    try:
        # Make the POST request
        response = requests.post(
            endpoint_url,
            headers={'Content-Type': 'application/json'},
            data=json.dumps(data),
            timeout=5  # Add timeout to prevent hanging
        )
        if response.status_code == 200:
            print(f"Succeeded POST: {response.text}")
        else:
            print(f"Failed POST...")
    except Exception as e:
        print(f"Error posting vehicles: {str(e)}")
    data["data"] = []
    time.sleep(10)
    print(responses)



# Main execution
import time
if __name__ == "__main__":
    # URL to the vehicle positions feed
    gtfs_url = "http://gtfs.viainfo.net/vehicle/vehiclepositions.pb"
    # URL to post data to
    post_url = "http://72.181.123.208:3035/stream/point"

    while True:
        try:
            print(f"Fetching GTFS Realtime data from: {gtfs_url}")
            # Parse the file from URL
            vehicles_df = parse_vehicle_positions(gtfs_url, is_url=True)
            post_vehicle_data_to_server(vehicles_df, post_url)
        except requests.exceptions.RequestException as e:
            print(f"Network error when fetching the data: {e}")
        except google.protobuf.message.DecodeError as e:
            print(f"Error decoding the protobuf message: {e}")
        except Exception as e:
            print(f"Unexpected error: {e}")