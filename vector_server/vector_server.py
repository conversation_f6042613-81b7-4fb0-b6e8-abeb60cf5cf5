import pandas as pd
from flask import Flask, request, jsonify
import chromadb
from sentence_transformers import SentenceTransformer

app = Flask(__name__)

# ------------------------------------------------------------------------------
# 1) Load CSV data
# ------------------------------------------------------------------------------
df = pd.read_csv("places_filtered.csv")  # Replace with your CSV file name

# ------------------------------------------------------------------------------
# 2) Initialize Chroma client and collection
# ------------------------------------------------------------------------------
client = chromadb.PersistentClient(path="chromadb_data")

# Create or get a collection
collection = client.get_or_create_collection("sa_places_collection")


# ------------------------------------------------------------------------------
# 3) Embedding model
# ------------------------------------------------------------------------------
embedder = SentenceTransformer("all-MiniLM-L6-v2")

# ------------------------------------------------------------------------------
# 4) Populate the ChromaDB collection
# ------------------------------------------------------------------------------
for idx, row in df.iterrows():
    name = row.get("Name", "")
    address_str = str(row.get("addresses", ""))
    
    # Combine for the embedding text
    embedding_text = f"{name} {address_str}"
    embedding_vector = embedder.encode(embedding_text).tolist()

    # Store relevant metadata (including lat/lon)
    collection.add(
        embeddings=[embedding_vector],
        documents=[embedding_text],
        metadatas=[{
            "Name": name,
            "Latitude": row.get("Latitude", None),
            "Longitude": row.get("Longitude", None)
        }],
        ids=[str(idx)]
    )

# ------------------------------------------------------------------------------
# 5) Flask endpoint: find-similar-name
# ------------------------------------------------------------------------------
@app.route("/find-similar-name", methods=["POST"])
def find_similar_name():
    """
    Expects JSON input with a "name" field, e.g.:
    {
      "name": "Alamo Dome"
    }

    Returns:
    {
      "similar_name": <the best-match name>,
      "metadata": {
          "latitude": ...,
          "longitude": ...
      }
    }
    or a JSON response with a 'message' explaining no match was found.
    """
    data = request.get_json()
    query_name = data.get("name", "")
    if not query_name:
        return jsonify({"error": "Please provide a 'name' in JSON"}), 400

    # Embed the query
    query_embedding = embedder.encode(query_name).tolist()

    # Search top-1 result
    results = collection.query(
        query_embeddings=[query_embedding],
        n_results=1
    )

    # If no results (unlikely in this simple example, but just in case)
    if not results["ids"] or len(results["ids"][0]) == 0:
        return jsonify({"message": "No match found"}), 404

    # Extract the best match
    best_distance = results["distances"][0][0]
    best_metadata = results["metadatas"][0][0]  # we stored lat/lon here
    best_name = best_metadata["Name"]

    # Example distance threshold check (L2 distance)
    threshold_distance = 1.5
    if best_distance < threshold_distance:
        return jsonify({
            "similar_name": best_name,
            "metadata": {
                "latitude": best_metadata["Latitude"],
                "longitude": best_metadata["Longitude"]
            }
        })
    else:
        # No match under threshold
        return jsonify({"best_distance": best_distance, "threshold_distance": threshold_distance, "message": "No match found"}), 404


if __name__ == "__main__":
    # For production, use a production-grade server. Debug only for local dev.
    app.run(debug=True)
