<html lang="en">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="initial-scale=1,maximum-scale=1,user-scalable=no" />
  <title>Heat Island Simulator</title>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet" />
  <link rel="stylesheet" type="text/css" href="https://js.arcgis.com/calcite-components/2.13.2/calcite.css" />
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <script type="module" src="https://js.arcgis.com/calcite-components/2.13.2/calcite.esm.js"></script>
  <style>
    html,
    body,
    #viewDiv {
      padding: 0;
      margin: 0;
      height: 98%;
      width: 100%;
      bottom: 0;
      position: absolute;
    }

    #info {
      background: rgb(218 209 220);
    }

    #connectionStatus {
      color: white;
      background: #e33a3a;
      padding: 8px 16px;
      display: none;
    }

    #updateRate {
      color: rgb(78, 78, 78);
      background: rgb(218 209 220);
      padding: 8px 16px;
      display: none;
    }

    #recordBtn {
      position: absolute;
      bottom: 20px;
      right: 20px;
      z-index: 9999;
      width: 60px;
      height: 60px;
      border-radius: 50%;
      border: none;
      cursor: pointer;
      background-color: #007bff;
      color: #fff;
      font-size: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    #recordBtn i {
      margin-right: 6px;
      font-size: 1.2em;
      margin: auto;
    }

    #recordBtn span {
      display: none;
      font-size: 0.9em;
      white-space: nowrap;
    }

    .esri-attribution {
      display: none !important;
    }

    .close-btn {
      background: none;
      border: none;
      color: #fff;
      font-size: 1.2em;
      cursor: pointer;
    }

    .close-btn:hover {
      color: #e74c3c;
    }

    .sidebar {
      top: 0;
      left: 0;
      height: 100%;
      width: 300px;
      background-color: white;
      box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
      transform: translateX(-100%);
      transition: transform 0.3s ease;
      display: flex;
      flex-direction: column;
    }

    .sidebar-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 13px;
      background-color: #f4f4f4;
      border-bottom: 1px solid #ddd;
    }

    .sidebar-header h3 {
      margin: 0;
    }

    .esri-component esri-expand esri-widget {
      width: fit-content !important;
    }

    .sidebar-content {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .sidebar-content li {
      padding: 15px 20px;
      border-bottom: 1px solid #f4f4f4;
    }

    .sidebar-content li a {
      text-decoration: none;
      color: black;
      font-size: 1em;
    }

    .sidebar-content li:hover {
      background-color: #f0f0f0;
    }

    .sidebar.open {
      transform: translateX(0);
    }

    calcite-icon[icon="compass-needle"] {
      margin-left: -4px;
    }

    .esri-time-slider {
      display: none;
    }

    .esri-weather__options--hidden {
      display: none !important;
    }
  </style>
  <link rel="stylesheet" href="https://js.arcgis.com/4.26/esri/themes/light/main.css" />
  <script src="https://js.arcgis.com/4.32/"></script>

</head>

<body>
  <button id="recordBtn">
    <i class="fas fa-microphone"></i> <span>Start Recording</span>
  </button>
  <div id="header"
    style="display: flex; align-items: center; vertical-align: middle; margin-top: -20px; padding-left: 15px; font-size: 1.1em;">
    <button id="hamburgerMenu"
      style="border: none; background: none; font-size: 1.5em; margin-right: 10px; cursor: pointer;">
      <i class="fas fa-bars"></i>
    </button>
    <span id="title">SATX 2024 - 3D City</span>
  </div>
  <div style="display: flex; padding-top:13px; overflow: hidden;">
    <div id="sidebarMenu" class="sidebar" style="flex-shrink: 0; overflow: hidden; transition: width 0.3s ease;">
      <ul class="sidebar-content" style="list-style: none; padding: 0; margin: 0;">
        <li
          style="display: flex; align-items: center; justify-content: space-between; padding: 10px; border-bottom: 1px solid #f4f4f4;">
          <a href="#option15" id="cityService"
            style="display: flex; align-items: center; text-decoration: none; color: black;">
            <i class="fas fa-city" style="margin-right: 10px; color: black; font-size: 1em; width: 15px;"></i>3D City
          </a>
          <button id="closeSidebar" class="close-btn"
            style="border: none; background: none; font-size: 1em; cursor: pointer; color: black;">X</button>
        </li>
      </ul>
      <div class="sidebar-header" style="background-color: #f4f4f4; padding: 10px; border-bottom: 1px solid #ddd;">
        <h3 style="color: black;">Services</h3>
      </div>
      <ul class="sidebar-content" style="list-style: none; padding: 0; margin: 0;">
        <li style="display: flex; align-items: center; padding: 10px; border-bottom: 1px solid #f4f4f4;">
          <a href="#option1" id="basicService" style="text-decoration: none; color: black;"><i
              class="fas fa-map-marked-alt" style="margin-right: 10px; color: black;width: 15px;"></i>Basic City</a>
        </li>
        <!--        <li style="display: flex; align-items: center; padding: 10px; border-bottom: 1px solid #f4f4f4;">-->
        <!--          <a href="#option2" style="text-decoration: none; color: black;"><i class="fas fa-home"-->
        <!--              style="margin-right: 10px; color: black;width: 15px;"></i>House Appraisals</a>-->
        <!--        </li>-->
        <li style="display: flex; align-items: center; padding: 10px; border-bottom: 1px solid #f4f4f4;">
          <a href="#option3" id="uhiService" style="text-decoration: none; color: black;"><i
              class="fas fa-temperature-high" style="margin-right: 10px; color: black;width: 15px;"></i>UHI Service</a>
        </li>
        <li style="display: flex; align-items: center; padding: 10px; border-bottom: 1px solid #f4f4f4;">
          <a id="waterService" href="#option4" style="text-decoration: none; color: black;">
            <i class="fas fa-water" style="margin-right: 10px; color: black;width: 15px;"></i>Flooding Service
          </a>
        </li>
        <li style="display: flex; align-items: center; padding: 10px; border-bottom: 1px solid #f4f4f4;">
          <a href="#option5" id="gridService" style="text-decoration: none; color: black;"><i class="fas fa-th"
              style="margin-right: 10px; color: black;width: 15px;"></i>Grid Service</a>
        </li>
        <li style="display: flex; align-items: center; padding: 10px; border-bottom: 1px solid #f4f4f4;">
          <a href="#option6" id="cadService" style="text-decoration: none; color: black;"><i class="fas fa-building"
              style="margin-right: 10px; color: black;width: 15px;"></i>Building view</a>
        </li>
      </ul>
    </div>

  </div>

  <div id="viewDiv" style="flex-grow: 1; transition: margin-left 0.3s ease;">
  </div>
  </div>
  <script>
    const viewDiv = document.getElementById('viewDiv');
    const hamburgerMenu = document.getElementById('hamburgerMenu');
    const closeSidebar = document.getElementById('closeSidebar');
    const sidebarMenu = document.getElementById('sidebarMenu');
    const sidebarWidth = 300;

    // Function to toggle the sidebar
    hamburgerMenu.addEventListener('click', () => {
      if (sidebarMenu.classList.contains('open')) {
        sidebarMenu.classList.remove('open');
        viewDiv.style.marginLeft = '0px';
        viewDiv.style.width = '100%';
      } else {
        sidebarMenu.classList.add('open');
        viewDiv.style.marginLeft = `${sidebarWidth}px`;
        viewDiv.style.width = `calc(100% - ${sidebarWidth}px)`;
      }
    });

    closeSidebar.addEventListener('click', () => {
      sidebarMenu.classList.remove('open');
      viewDiv.style.marginLeft = '0px';
      viewDiv.style.width = '100%';
    });
  </script>
  <script>
    window.app = {
      view: null,
      makeRain: null,
      makeSunny: null,
      makeFog: null,
      makeNight: null,
      makeDay: null,
      goTo: null,
      goToCoordinate: null,
      openBasic: null,
      openUHI: null,
      open3DCity: null,
      openGrid: null,
      toggleVia: null,
      openDataScience: null,
    };
    window.address = "{{ host }}"
    window.gpsFlag = "{{ gpsFlag }}";
    window.printCameraPositionToConsole = null;
    window.app.updatePrompt = null;
    require([
      "esri/WebScene",
      "esri/layers/IntegratedMesh3DTilesLayer",
      "esri/widgets/Slice",
      "esri/analysis/SlicePlane",
      "esri/Camera",
      "esri/layers/StreamLayer",
      "esri/request",
      "esri/identity/IdentityManager",
      "esri/views/SceneView",
      "esri/layers/BuildingSceneLayer",
      "esri/layers/FeatureLayer",
      "esri/widgets/Weather",
      "esri/widgets/Daylight",
      "esri/widgets/TimeSlider",
      "esri/widgets/Zoom",
      "esri/widgets/Legend",
      "esri/widgets/Expand",
      "esri/widgets/TimeZoneLabel",
      "esri/layers/GraphicsLayer",
      "esri/layers/SceneLayer",
      "esri/layers/TileLayer",
      "esri/request"
    ], function (WebScene, IntegratedMesh3DTilesLayer, Slice, SlicePlane, Camera, StreamLayer, esriRequest, IdentityManager, SceneView, BuildingSceneLayer, FeatureLayer, Weather, Daylight, TimeSlider, Zoom, Legend, Expand, TimeZoneLabel, GraphicsLayer, SceneLayer, TileLayer, esriRequest) {
      async function initializeMap() {
        async function initToken() { //I will change the password later. We Will also move this to .env file when you get an account.
          const username = "{{ username }}"
          const token = "{{ token }}"
          IdentityManager.registerToken({
            server: "https://utsa.maps.arcgis.com",
            token: token,
            userId: username
          });
          return token;
        }
        const token = await initToken();

        const cameraLocations = [
          {
            name: 'StartingPosition',
            cameraOptions: {
              position: {
                x: -98.5080,
                y: 29.4250,
                z: 800
              },
              heading: 90,
              tilt: 65
            }
          },
          {
            name: 'America',
            cameraOptions: {
              position: {
                x: -98.4830,
                y: 29.4078,
                z: 800
              },
              heading: 0,
              tilt: 65
            }
          },
          {
            name: 'Alamo Dome',
            cameraOptions: {
              position: {
                x: -98.4700,
                y: 29.4170,
                z: 800
              },
              heading: -90,
              tilt: 55
            }
          },
          {
            name: 'UHI',
            cameraOptions: {
              position: {
                x: -98.4830,
                y: 29.4000,
                z: 1400
              },
              heading: 0,
              tilt: 65
            }
          },
          {
            name: '3DCity',
            cameraOptions: {
              position: {
                x: -98.4830,
                y: 29.4150,
                z: 900
              },
              heading: 0,
              tilt: 50
            }
          },
          {
            name: 'Power',
            cameraOptions: {
              position: {
                x: -98.6197,
                y: 29.5817,
                z: 720
              },
              heading: -40,
              tilt: 60
            }
          },
          {
            name: 'Library',
            cameraOptions: {
              position: {
                x: -98.5040,
                y: 29.4390,
                z: 800
              },
              heading: 125,
              tilt: 63
            }
          },
          {
            name: 'DataScience',
            cameraOptions: {
              "position": {
                "x": -98.4961,
                "y": 29.4305,
                "z": 524
              },
              "heading": 178,
              "tilt": 67
            }
          },
          {
            name: 'FrostTower',
            cameraOptions: {
              position: {
                x: -98.4970,
                y: 29.4360,
                z: 800
              },
              heading: 169,
              tilt: 59
            }
          },
          {
            name: 'DataScience',
            cameraOptions: {
              position: {
                x: -98.495850,
                y: 29.423760,
                z: 219
              },
              heading: 104,
              tilt: 86
            }
          }
        ];
        const cameraMap = new Map(
          cameraLocations.map(location => [location.name, location.cameraOptions])
        );

        const google3DTiles = new IntegratedMesh3DTilesLayer({
          url: "https://tile.googleapis.com/v1/3dtiles/root.json",
          title: "Google tiles",
          customParameters: {
            // see https://developers.google.com/maps/documentation/tile/3d-tiles-overview
            "key": "AIzaSyBs91cGrEkkESlZTy8AxbGy2wzlfVOfhG4"
          }
        });

        function fly(cameraOptions, duration = 3000) {
          window.app.view.goTo(cameraOptions, {
            duration: duration,  // Duration in milliseconds (3 seconds)
            easing: "out-cubic"  // Smooth easing function
          });
        }
        const fullScene = new WebScene({
          portalItem: {
            id: "e3f45c1ee21e42c59d619ff0846ab6ca"
          }
        });
        let view = new SceneView({
          container: "viewDiv",
          map: fullScene,
          center: [-98.4936, 29.426],
          zoom: 18,
          camera: cameraMap.get('StartingPosition'),
          environment: {
            weather: {
              type: "sunny",
              cloudCover: 0.2,
              precipitation: 0.0
            }
          }
        });
        window.printCameraPositionToConsole = function printCameraPositionToConsole() {
          const camera = view.camera;
          console.log(JSON.stringify({
            position: {
              x: Number(camera.position.longitude.toFixed(4)),
              y: Number(camera.position.latitude.toFixed(4)),
              z: Number(camera.position.z.toFixed(0))
            },
            heading: Number(camera.heading.toFixed(0)),
            tilt: Number(camera.tilt.toFixed(0))
          }, null, 2));
        };

        const stream = new StreamLayer({
          popupTemplate: {
            title: "Route {ROUTE}"
          },
          webSocketUrl: "wss://{{ host }}/via",
          objectIdField: "OBJECTID",
          fields: [
            {
              name: "OBJECTID",
              alias: "ObjectId",
              type: "oid",
            },
            {
              name: "TRACKID",
              alias: "TrackId",
              type: "oid",
            },
            {
              name: "ROUTE",
              alias: "Route",
              type: "oid",
            },
            {
              name: "TIMESTAMP",
              alias: "Timestamp",
              type: "date",
            }
          ],
          timeInfo: {
            trackIdField: "TRACKID"
          },
          geometryType: "point",
          maxReconnectionAttempts: 100,
          maxReconnectionInterval: 100,
          renderer: {
            type: "simple",
            symbol: {
              type: "simple-marker",
              size: "8px",
              color: "red",
            },
          },
          elevationInfo: {
            mode: "relative-to-scene",
            offset: 7,  // Height in meters above the scene
            unit: "meters"
          },
          purgeOptions: {
            displayCount: 10000,
            maxObservations: 60
          }
        });


        function filterLatestPointForTrack() {
          view.whenLayerView(stream).then(layerView => {
            // Query features for the specified track
            const query = {
              outFields: ["TRACKID", "OBJECTID", "TIMESTAMP", "ROUTE"],
              // where: "TRACKID in ('985','555','987')", // Filter by track ID
              orderByFields: ["TIMESTAMP DESC"], // Order by timestamp descending (newest first)
              returnGeometry: false,
              num: 100000
            };
            let uniqueTrackIds = new Set();
            let objectsIds = []
            layerView.queryFeatures(query)
              .then(result => { // Each result is a location from query
                if (result.features && result.features.length > 0) {
                  result.features.forEach(feature => {
                    const objectId = feature.attributes.OBJECTID;
                    const trackId = feature.attributes.TRACKID;
                    if (uniqueTrackIds.has(trackId))
                      ;
                    else {
                      // console.log(trackId)
                      uniqueTrackIds.add(trackId)
                      objectsIds.push(objectId)
                    }
                  });
                  let clause = "OBJECTID IN ('"
                  let objectsList = objectsIds.join("','")
                  clause += objectsList + "')"
                  // console.log(clause)
                  layerView.filter = {
                    where: clause
                  };
                }
              });
          }).catch(err => {
            console.error("Error getting layer view:", err);
          });
        }
        setInterval(filterLatestPointForTrack, 1000);


        const info = document.getElementById("info");
        view.whenLayerView(stream).then(layerView => {
          layerView.on("update-rate", (updateRate) => {
            if (updateRate.websocket > 0)
              window.gpsFlag = "T"
            updateGpsStatus()
          })
        }).catch(e => console.log(e))

        //Start BIM
        const buildingLayer = new BuildingSceneLayer({
          portalItem: {
            id: "d18fcd78b83946f180525c1e7f02e1fd"
          },
          title: "Data Science Building",
          popupEnabled: true,
          sublayers: []
        });

        const plane = new SlicePlane({
          position: {
            latitude: 29.4239,
            longitude: -98.49578,
            z: 223
          },
          tilt: -32.62,
          width: 33,
          height: 29,
          heading: 0.46
        });
        let sliceWidget = null;
        let sliceTiltEnabled = true;
        let sliceActive = false;
        function setBuildingDetail(on) {
          if (!on) {
            buildingLayer.allSublayers.forEach((layer) => {
              // console.log(layer.modelName)
              switch (layer.modelName) {
                case "FullModel":
                  layer.visible = true;
                  break;
                case "Planting":
                case "Overview":
                case "Rooms":
                  layer.visible = false;
                  break;
                default:
                  layer.visible = true;
              }
            });
          } else {
            buildingLayer.allSublayers.forEach((layer) => {
              switch (layer.modelName) {
                case "FullModel":
                  layer.visible = false;
                  break;
                case "Overview":
                  layer.visible = true;
                  break;
                case "Planting":
                case "Rooms":
                  layer.visible = false;
                  break;
                default:
                  layer.visible = false;
              }
            });
          }
        }
        setSliceWidget()

        // Add double-click event handler to toggle slice widget
        view.on("double-click", (event) => {
          event.stopPropagation();
          // Check if the double-click is on the building layer
          view.hitTest(event).then((response) => {
            const buildingHit = response.results.some(result => {
              return result.graphic && result.graphic.layer &&
                (result.graphic.layer === buildingLayer ||
                  (result.graphic.layer.parent && result.graphic.layer.parent === buildingLayer));
            });

            if (buildingHit) {
              toggleSliceWidget();
            }
          });
        });

        function setSliceWidget() {
          sliceWidget = new Slice({
            view: view
          });
          sliceTiltEnabled = true;
          sliceWidget.viewModel.tiltEnabled = sliceTiltEnabled;
          sliceWidget.viewModel.shape = plane;
          sliceWidget.viewModel.clear();
          sliceActive = false;
        }

        function toggleSliceWidget() {
          setBuildingDetail(sliceActive);
          if (sliceActive) {
            sliceWidget.viewModel.clear();
            sliceActive = false;
          } else {
            sliceWidget.viewModel.shape = plane;
            sliceActive = true;
          }
        }
        //End BIM

        fullScene.add(stream);


        fullScene.when(() => {
          fullScene.allLayers.forEach((layer) => {
            console.log(layer.title)
            if (layer.title === "Service_and_Utility") {
              // Create a definition expression to exclude OBJECTID 77
              layer.definitionExpression = "OBJECTID <> 77";
              layer.visible = false;
            }
            if (layer.title === "lod3_base") {
              // Create a definition expression to exclude OBJECTID 77
              layer.definitionExpression = "OBJECTID <> 34109";
            }
            if (layer.title === "multipatch_lodless") {
              layer.visible = false;
            }
            if (layer.title.includes("Polygons")) {
              layer.popupEnabled = true
              layer.renderer = {
                type: "simple",
                symbol: {
                  type: "polygon-3d",
                  symbolLayers: [{
                    type: "fill",
                    material: {
                      color: [0, 0, 0, 0.01]
                    },
                    outline: {
                      color: [0, 0, 0, 0.01],
                      width: 0.5
                    }
                  }]
                }
              }
              layer.elevationInfo = {
                mode: "on-the-ground",  // Changed from "relative-to-scene" to ensure it wraps on top
                offset: 1,              // Small positive offset to ensure it's above the ground
                featureExpressionInfo: {
                  expression: "1"       // Changed from "0" to ensure it's slightly above ground level
                },
                unit: "meters"
              };
              layer.popupTemplate = {
                title: "{expression/buildingName}",
                expressionInfos: [{
                  name: "buildingName",
                  title: "Building Name",
                  expression: "Dictionary($feature.names).primary"
                }],
                content: [{
                  type: "fields",
                  fieldInfos: [{
                    fieldName: "expression/buildingName",
                    label: "Building Name"
                  }]
                }],
                outFields: ["names"]
              }
            } else
              layer.popupEnabled = false
            if (layer.title.includes("OH_Primary") || layer.title.includes("Substation") || layer.title.includes("412_") || layer.title.includes("Zone_") || layer.title.includes("final_NW")) {
              layer.popupEnabled = true
            }

          })
          selectGroupLayer("3DCity")
        });

        //HeatIsland, 3DCity, Power
        function selectGroupLayer(groupLayerTitle) {
          if (groupLayerTitle === "3DCity") {
            fullScene.add(google3DTiles);
          } else {
            fullScene.remove(google3DTiles);
          }
          if (groupLayerTitle === "HeatIsland") {
            fullScene.add(buildingLayer);
          } else {
            fullScene.remove(buildingLayer);
          }
          fullScene.layers.forEach(layer => {
            if (layer.type === "group") {
              layer.visible = layer.title === groupLayerTitle;
              console.log(`Group layer "${layer.title}" visibility set to: ${layer.title === groupLayerTitle}`);
            }
          });
        }



        window.app.view = view;
        window.app.makeRain = () => {
          window.app.view.environment.weather = {
            type: "rainy",
            cloudCover: 0.7,
            precipitation: 0.3
          };
        };
        window.app.makeSunny = () => {
          window.app.view.environment.weather = {
            type: "sunny",
            cloudCover: 0.1,
            precipitation: 0.0
          };
        };
        window.app.makeFog = () => {
          window.app.view.environment.weather = {
            type: "foggy",
            cloudCover: 0.2,
            precipitation: 0.0,
            fogStrength: 0.35
          };
        };
        window.app.toggleVia = () => {
          toggleVia()
        }

        function stepToTime(hour) {
          const currentDate = view.environment.lighting.date;
          const targetDate = new Date(currentDate);
          targetDate.setHours(hour, 0, 0, 0);

          if (targetDate.getTime() < currentDate.getTime()) {
            targetDate.setDate(targetDate.getDate() + 1);
          }

          const duration = 6000;
          const startTime = performance.now();
          const startDate = new Date(currentDate);

          if (window.timeAnimation) {
            clearTimeout(window.timeAnimation);
            window.timeAnimation = null;
          }

          function animateStep(timestamp) {
            let progress = (timestamp - startTime) / duration;
            progress = Math.min(progress, 1);
            progress = progress < 0.5 ?
              4 * progress * progress * progress :
              1 - Math.pow(-2 * progress + 2, 3) / 2;

            const newTime = new Date(
              startDate.getTime() + (targetDate.getTime() - startDate.getTime()) * progress
            );
            view.environment.lighting.date = newTime;
            if (progress < 1)
              window.timeAnimation = requestAnimationFrame(animateStep);
            else {
              view.environment.lighting.date = targetDate;
              window.timeAnimation = null;
            }
          }
          window.timeAnimation = requestAnimationFrame(animateStep);
        }

        window.app.makeDay = () => {
          stepToTime(17)
        };

        window.app.makeNight = () => {
          stepToTime(1)
        };

        function selectByObjectID(objectID) {
          fullScene.allLayers.forEach((layer) => {
            if (layer.title.includes("Polygons")) {

              // Create a query to find the Tower of the Americas
              const query = layer.createQuery();
              query.where = "OBJECTID = " + objectID.toString(); // Direct query using the OBJECTID we can see in the image
              query.returnGeometry = true;
              query.outFields = ["*"];

              layer.queryFeatures(query)
                .then((result) => {
                  if (result.features && result.features.length > 0) {
                    const feature = result.features[0];
                    console.log("Found building:", feature.attributes);

                    // Clear any existing popup
                    if (view.popup) {
                      if (typeof view.popup.clear === 'function') {
                        view.popup.clear();
                      } else if (typeof view.popup.visible !== 'undefined') {
                        view.popup.visible = false;
                      }
                    }
                    view.popup.visible = false;
                    view.whenLayerView(layer).then(function (layerView) {
                      if (window.highlightHandle) {
                        window.highlightHandle.remove();
                        window.highlightHandle = null;
                      }

                      window.highlightHandle = layerView.highlight(feature);

                      if (view.popup) {
                        if (typeof view.popup.set === 'function') {
                          view.popup.set({
                            features: [feature],
                            location: feature.geometry.centroid
                          });
                          if (typeof view.popup.visible !== 'undefined') {
                            view.popup.visible = false;
                          }
                        } else if (typeof view.popup.features !== 'undefined') {
                          view.popup.features = [feature];
                          view.popup.location = feature.geometry.centroid;
                          if (typeof view.popup.visible !== 'undefined') {
                            view.popup.visible = false;
                          }
                        }
                      }
                    });
                  }
                })

            }
          });
        }
        selectByCoordinate = (latitude, longitude) => {
          console.log("code updated")
          const point = {
            type: "point",
            longitude: longitude,
            latitude: latitude
          };

          fullScene.allLayers.forEach((layer) => {
            if (layer.title.includes("Polygons")) {
              // Create a spatial query to find polygons containing the point
              const query = layer.createQuery();
              query.geometry = point;
              query.spatialRelationship = "intersects";
              query.returnGeometry = true;
              query.outFields = ["*"];

              layer.queryFeatures(query)
                .then((result) => {
                  if (result.features && result.features.length > 0) {
                    const feature = result.features[0];
                    console.log("Found building:", feature.attributes);

                    // Clear any existing popup
                    if (view.popup) {
                      if (typeof view.popup.clear === 'function') {
                        view.popup.clear();
                      } else if (typeof view.popup.visible !== 'undefined') {
                        view.popup.visible = false;
                      }
                    }

                    // Highlight the selected feature
                    view.whenLayerView(layer).then(function (layerView) {
                      if (window.highlightHandle) {
                        window.highlightHandle.remove();
                        window.highlightHandle = null;
                      }

                      window.highlightHandle = layerView.highlight(feature);

                      // Show building info in popup (optional)
                      if (view.popup) {
                        if (typeof view.popup.set === 'function') {
                          view.popup.set({
                            features: [feature],
                            location: feature.geometry.centroid
                          });
                          view.popup.visible = true;
                        } else if (typeof view.popup.features !== 'undefined') {
                          view.popup.features = [feature];
                          view.popup.location = feature.geometry.centroid;
                          view.popup.visible = true;
                        }
                      }

                      // Optionally fly to the building
                      // Optionally fly to the building
                      let cameraOptions;

                      cameraOptions = {
                        position: {
                          x: longitude,
                          y: latitude - 0.01,
                          z: 800
                        },
                        heading: 0,
                        tilt: 65
                      };
                      fly(cameraOptions)
                    });
                  } else {
                    console.log("No building found at coordinates:", latitude, longitude);
                  }
                })
                .catch(error => {
                  console.error("Error querying features:", error);
                });
            }
          });
        };
        window.app.goToCoordinate = (latitude, longitude) => {
          console.log("Going To: " + latitude + ", " + longitude)
          let cameraOptions;

          cameraOptions = {
            position: {
              x: longitude,
              y: latitude - 0.01,
              z: 800
            },
            heading: 0,
            tilt: 65
          };
          // fly(cameraOptions)
          selectByCoordinate(latitude, longitude)

        };



        window.app.goTo = (buildingName) => {
          let cameraOptions;
          if (buildingName === "america") {
            cameraOptions = cameraMap.get("America");
            selectByObjectID(254086)
          }
          else if (buildingName === "dome") {
            cameraOptions = cameraMap.get("Alamo Dome");
            selectByObjectID(263094)
          }
          else if (buildingName === "science") {
            cameraOptions = cameraMap.get("DataScience");
            selectByObjectID(258611)
          }
          else if (buildingName === "library") {
            cameraOptions = cameraMap.get("Library");
            selectByObjectID(259470)
          }
          else if (buildingName === "frost") {
            cameraOptions = cameraMap.get("FrostTower");
            selectByObjectID(258761)
          }
          if (cameraOptions) {
            fly(cameraOptions);
          }

        };

        window.app.openUHI = () => {
          cameraOptions = cameraMap.get("UHI")
          selectGroupLayer('HeatIsland')
          fly(cameraOptions)
          fullScene.add(temporalTemperature)
          temporalTemperature.popupEnabled = false;
          title.innerText = "SATX 2024 - Heat Island"
          const timeSlider = document.getElementsByClassName('esri-time-slider')[0];
          const weatherSlider = document.getElementsByClassName('esri-ui-top-right')[0];
          weatherSlider.style.display = "none";
          timeSlider.style.display = "block";
        }

        window.app.openBasic = () => {
          cameraOptions = cameraMap.get("UHI")
          selectGroupLayer('HeatIsland')
          fly(cameraOptions)
          fullScene.remove(temporalTemperature)
          temporalTemperature.popupEnabled = false;
          title.innerText = "SATX 2024 - Heat Island"
          const timeSlider = document.getElementsByClassName('esri-time-slider')[0];
          const weatherSlider = document.getElementsByClassName('esri-ui-top-right')[0];
          weatherSlider.style.display = "flex";
          timeSlider.style.display = "none";
        }

        window.app.open3DCity = () => {
          cameraOptions = cameraMap.get("3DCity")
          selectGroupLayer('3DCity')
          fly(cameraOptions)
          fullScene.remove(temporalTemperature)
          title.innerText = "SATX 2024 - 3D City"
          const timeSlider = document.getElementsByClassName('esri-time-slider')[0];
          const weatherSlider = document.getElementsByClassName('esri-ui-top-right')[0];
          weatherSlider.style.display = "flex";
          timeSlider.style.display = "none";
        }

        window.app.openGrid = () => {
          cameraOptions = cameraMap.get("Power")
          selectGroupLayer('Power')
          fly(cameraOptions, 8000)
          fullScene.remove(temporalTemperature)
          title.innerText = "SATX 2024 - Power Grid"
          const timeSlider = document.getElementsByClassName('esri-time-slider')[0];
          const weatherSlider = document.getElementsByClassName('esri-ui-top-right')[0];
          weatherSlider.style.display = "flex";
          timeSlider.style.display = "none";
        }
        window.app.openDataScience = () => {
          cameraOptions = cameraMap.get("DataScience")
          selectGroupLayer('HeatIsland')
          fly(cameraOptions)
          fullScene.remove(temporalTemperature)
          temporalTemperature.popupEnabled = false;
          title.innerText = "SATX 2024 - Data Science"
          const timeSlider = document.getElementsByClassName('esri-time-slider')[0];
          const weatherSlider = document.getElementsByClassName('esri-ui-top-right')[0];
          weatherSlider.style.display = "flex";
          timeSlider.style.display = "none";
          buildingLayer.when(() => {
            if (sliceActive == false)
              setTimeout(toggleSliceWidget, 3500)
          });
        }

        const temporalTemperature = new FeatureLayer({
          portalItem: {
            id: "02795552320b48bda2e49d9995057921",
          },
          timeInfo: {
            startField: "AcquisitionDate",
            endField: "AcquisitionDate",
            timeExtent: {
              start: new Date(1577836800000),  // Your earliest timestamp
              end: new Date(1577836800000 + (365 * 24 * 60 * 60 * 1000))  // One year later
            }
          }
        });

        function colorRampPrediction(min, max, value) {
          const normalizedValue = (value - min) / (max - min);
          const startColor = { r: 0, g: 128, b: 128 };
          const midColor = { r: 255, g: 255, b: 0 };
          const endColor = { r: 255, g: 0, b: 0 };

          let r, g, b;
          if (normalizedValue < 0.5) {
            const t = normalizedValue * 2;
            r = Math.round(startColor.r + t * (midColor.r - startColor.r));
            g = Math.round(startColor.g + t * (midColor.g - startColor.g));
            b = Math.round(startColor.b + t * (midColor.b - startColor.b));
          } else {
            const t = (normalizedValue - 0.5) * 2;
            r = Math.round(midColor.r + t * (endColor.r - midColor.r));
            g = Math.round(midColor.g + t * (endColor.g - midColor.g));
            b = Math.round(midColor.b + t * (endColor.b - midColor.b));
          }

          return `rgb(${r}, ${g}, ${b})`;
        }

        async function rampLSTToPrediction(lstLayer, valueString, fieldName = valueString) {
          try {
            const result = await lstLayer.queryFeatures({
              where: "1=1",
              outStatistics: [
                {
                  statisticType: "min",
                  onStatisticField: valueString,
                  outStatisticFieldName: "minValue"
                },
                {
                  statisticType: "max",
                  onStatisticField: valueString,
                  outStatisticFieldName: "maxValue"
                }
              ]
            });

            const popupTemplate = {
              title: "Weather",
              content: [{
                type: "fields",
                fieldInfos: [{
                  fieldName: fieldName,
                  label: "Temperature (°F)",
                  visible: false
                }]
              }]
            };

            const stats = result.features[0].attributes;
            const minValue = stats.minValue;
            const maxValue = stats.maxValue;
            const uniqueValueInfos = [];
            for (let value = minValue; value <= maxValue; value++) {
              uniqueValueInfos.push({
                value: value,
                symbol: {
                  type: "simple-fill",
                  color: colorRampPrediction(minValue, maxValue, value),
                  outline: {
                    width: 0,
                  }
                }
              });
            }

            const renderer = {
              type: "unique-value",
              field: valueString,
              uniqueValueInfos: uniqueValueInfos
            };
            lstLayer.renderer = renderer;
            lstLayer.popupTemplate = popupTemplate;
            lstLayer.opacity = 0.5;
          } catch (error) {
            console.error("Error querying features:", error);
          }
        }

        temporalTemperature.when(async () => {
          try {
            await rampLSTToPrediction(temporalTemperature, "rank", "gridcode");
          } catch (error) {
            console.error("Error applying renderer to layer:", error);
          }
        });
        temporalTemperature.load().then(async () => {
          const uniqueDatesQuery = await temporalTemperature.queryFeatures({
            where: "1=1",
            outFields: ["AcquisitionDate"],
            returnDistinctValues: true,
            orderByFields: "AcquisitionDate"
          });

          const uniqueDates = uniqueDatesQuery.features
            .map(f => f.attributes.AcquisitionDate)
            .sort((a, b) => a - b);

          const timeSlider = new TimeSlider({
            container: "timeSlider",
            view: view,
            mode: "instant",
            fullTimeExtent: {
              start: new Date(uniqueDates[0]),
              end: new Date(uniqueDates[uniqueDates.length - 1])
            },
            stops: {
              dates: uniqueDates.map(date => new Date(date))
            },
            layout: "auto",
            timeVisible: true,
            loop: true,
            playRate: 2000,
            labelFormatFunction: (value) => {
              return new Date(value).toLocaleDateString();
            }
          });

          view.ui.add(timeSlider, "bottom-right");
        }).catch((error) => {
          console.error("Error loading layer:", error);
        });

        const zoom = new Zoom({
          view: view
        });
        view.ui.add(zoom, "top-left");

        // Create a new div element for the text box widget
        const textBoxWidget = document.createElement("div");
        textBoxWidget.id = "textBoxWidget";
        textBoxWidget.className = "esri-widget";
        textBoxWidget.style.display = "flex";
        textBoxWidget.style.flexDirection = "row";
        textBoxWidget.style.backgroundColor = "#4e4e4e";
        textBoxWidget.style.color = "white";
        textBoxWidget.style.padding = "2px";
        textBoxWidget.style.borderRadius = "4px";
        textBoxWidget.style.boxShadow = "0 1px 4px rgba(0,0,0,0.3)";

        // Create the editable text area
        const textArea = document.createElement("textarea");
        textArea.id = "prompt";
        textArea.style.backgroundColor = "#333";
        textArea.style.color = "white";
        textArea.style.border = "1px solid #555";
        textArea.style.borderRadius = "3px";
        textArea.style.width = "fit-content";
        textArea.style.maxHeight = "21px";
        textArea.style.resize = "none";
        textArea.style.fontFamily = "inherit";
        textArea.style.fontSize = "13px";
        textArea.placeholder = "_";

        window.app.updatePrompt = function (text) {
          textArea.value = text;
        }

        // Add focus/blur effects for better UX
        textArea.addEventListener("focus", () => {
          textArea.style.borderColor = "#888";
        });

        textArea.addEventListener("blur", () => {
          textArea.style.borderColor = "#555";
        });

        // Add keydown event listener to handle Enter key
        textArea.addEventListener("keydown", function (event) {
          if (event.key === "Enter") {
            event.preventDefault();

            // Process the entered text
            const enteredText = this.value;
            console.log("Text entered:", enteredText);
            interpretCommand(enteredText, false);
            this.value = "";
            this.placeholder = "_";
          }
        });

        // Add the textarea to the container
        textBoxWidget.appendChild(textArea);

        // Add the widget to the bottom-left of the view
        view.ui.add(textBoxWidget, "bottom-left");

        const legend = new Legend({
          view: view
        });

        const expand = new Expand({
          view: view,
          content: legend,
          mode: "floating"
        });
        view.ui.add(expand, "top-left");

        const weatherExpand = new Expand({
          view: view,
          content: new Weather({
            view: view
          }),
          group: "top-right",
          expanded: true
        });

        const daylightExpand = new Expand({
          view: view,
          content: new Daylight({
            view: view
          }),
          group: "top-right"
        });

        const gpsStatus = document.createElement("div");
        gpsStatus.id = "gpsStatus";
        gpsStatus.className = "esri-widget";
        gpsStatus.style.backgroundColor = "#4e4e4e";
        gpsStatus.style.color = "white";
        gpsStatus.style.padding = "8px 11.5px";
        gpsStatus.style.display = "inline-flex";
        gpsStatus.innerHTML = "V";
        view.ui.add([/*info, */weatherExpand, daylightExpand, gpsStatus], "top-right");
        function updateGpsStatus() {
          if (window.gpsFlag === "T") {
            gpsStatus.style.backgroundColor = "#d22a2a";
            stream.visible = true;
          } else {
            gpsStatus.style.backgroundColor = "#4e4e4e";
            stream.visible = false;
          }
        }
        function toggleVia() {
          window.gpsFlag = window.gpsFlag === "T" ? "F" : "T";
          const isTracking = window.gpsFlag === "T";
          const data = {
            "flag": isTracking,
          };
          fetch('https://{{ host }}/stream/monitor', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
          })
            .then(response => {
              if (response.status === 200) {
                console.log("Tracking status changed to:", isTracking);
              } else {
                console.error("Server returned status:", response.status);
                // Revert if server didn't accept the change
                window.gpsFlag = window.gpsFlag === "T" ? "F" : "T";
              }
              updateGpsStatus();
              return response.json();
            })
            .catch(error => {
              console.error('Error:', error);
            });
        }
        gpsStatus.addEventListener('click', toggleVia);
        //Toggle UHI
        const cityService = document.getElementById('cityService');
        const basicService = document.getElementById('basicService');
        const uhiService = document.getElementById('uhiService');
        // const waterService = document.getElementById('waterService');
        const gridService = document.getElementById('gridService');
        const title = document.getElementById('title');
        const cadService = document.getElementById('cadService');
        cityService.addEventListener('click', () => {
          window.app.open3DCity()
        })
        basicService.addEventListener('click', () => {
          window.app.openBasic()
        })
        uhiService.addEventListener('click', () => {
          window.app.openUHI()
        })
        gridService.addEventListener('click', () => {
          window.app.openGrid()
        })
        cadService.addEventListener('click', () => {
          window.app.openDataScience()
        })
        // waterService.addEventListener('click', () => {
        //   //window.app.openWater() Do this last
        // })
      };
      initializeMap();

    });
  </script>
  <script src="app.js"></script>
</body>

</html>