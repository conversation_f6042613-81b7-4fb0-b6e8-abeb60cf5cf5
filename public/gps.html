<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <style>
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
        }

        .toggle-button {
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
        }
    </style>
</head>

<body>
    <button id="trackingButton" class="toggle-button">Tracking</button>
    <script>
        const trackingButton = document.getElementById('trackingButton');
        let isTracking = false;
        function toggleTracking() {
            isTracking = !isTracking;
            toggleVia();
            if (isTracking) {
                trackingButton.textContent = 'Tracking: ON';
            } else {
                trackingButton.textContent = 'Tracking: OFF';
            }
        }
        function toggleVia() {
            const data = {
                "flag": isTracking,
            };

            fetch('https://{{ host }}/stream/monitor', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
                .then(response => {
                    console.log("Tracking: ", isTracking);
                    return response.json();
                })
                .catch(error => {
                    console.error('Error:', error);
                });
        }
        trackingButton.addEventListener('click', toggleTracking);
    </script>
</body>

</html>