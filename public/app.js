const RECORD_BTN = document.getElementById("recordBtn");

let stream = null;
let mediaRecorder = null;
let dgSocket = null;

let isRecording = false;
let currentUserTranscript = "";
let interpretCommandTimer = null;
const INTERPRET_COMMAND_DELAY = 0;

let currentAudioElement = null;
let audioContext = null;
let analyser = null;
let animationFrameId = null;

RECORD_BTN.addEventListener("click", () => {
  if (currentAudioElement) {
    // If audio is playing, stop it
    currentAudioElement.pause();
    currentAudioElement = null;
    if (animationFrameId) {
      cancelAnimationFrame(animationFrameId);
      animationFrameId = null;
    }
    if (audioContext) {
      audioContext.close().catch(console.error);
      audioContext = null;
      analyser = null;
    }
    RECORD_BTN.innerHTML = '<i class="fas fa-microphone"></i> <span>Start Recording</span>';
  } else {
    // Normal recording toggle behavior
    isRecording ? stopRecording() : startRecording();
  }
});

/**
 * Starts recording audio from the user's microphone and streams it via WebSocket to the server.
 * @returns {Promise<void>}
 */
async function startRecording() {
  console.log("Starting recording...");
  isRecording = true;
  RECORD_BTN.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>Connecting...</span>';
  currentUserTranscript = "";
  try {
    stream = await navigator.mediaDevices.getUserMedia({ audio: true });
  } catch (err) {
    console.error("Error accessing microphone:", err);
    isRecording = false;
    RECORD_BTN.innerHTML = '<i class="fas fa-microphone"></i> <span>Start Recording</span>';
    return;
  }
  dgSocket = new WebSocket("wss://{{ host }}/ws");
  dgSocket.addEventListener("open", () => {
    console.log("WebSocket connected.");
    RECORD_BTN.innerHTML = '<i class="fas fa-square"></i> <span>Stop Recording</span>';
    const mimeType = MediaRecorder.isTypeSupported("audio/webm;codecs=opus")
      ? "audio/webm;codecs=opus"
      : "audio/webm";
    console.log("Using MIME type:", mimeType);
    mediaRecorder = new MediaRecorder(stream, { mimeType });
    mediaRecorder.addEventListener("dataavailable", (event) => {
      if (event.data.size > 0 && dgSocket.readyState === WebSocket.OPEN) {
        dgSocket.send(event.data);
      }
    });
    mediaRecorder.start(500);
  });
  dgSocket.addEventListener("message", (event) => {
    let messageData;
    try {
      messageData = JSON.parse(event.data);
    } catch (err) {
      console.error("Error parsing WebSocket message:", err, event.data);
      return;
    }
    switch (messageData.type) {
      case "transcript": {
        const transcriptData = messageData.data.channel.alternatives[0];
        const newTranscript = transcriptData.transcript.trim();
        if (!newTranscript) return;

        if (messageData.data.is_final) {
          currentUserTranscript += ` ${newTranscript}`;
          console.log("User Final Transcript:", currentUserTranscript);
          window.app.updatePrompt(currentUserTranscript);

          if (interpretCommandTimer) {
            clearTimeout(interpretCommandTimer);
          }

          interpretCommandTimer = setTimeout(() => {
            if (isRecording) {
              interpretCommand(currentUserTranscript, true);
            }
            interpretCommandTimer = null;
          }, INTERPRET_COMMAND_DELAY);
        } else {
          console.log("User Partial Transcript:", newTranscript);
        }
        break;
      }
      case "chatMessage":
        console.log("User said:", messageData.content);
        break;
      case "chatResponse":
        if (messageData.streaming) {
          console.log("Assistant Partial:", messageData.content);
        } else {
          console.log("Assistant Final:", messageData.content);
          playTTSFromServer(messageData.content);
        }
        break;
      case "error":
        console.error("Server error:", messageData.message);
        break;
      default:
        console.log("Unknown message from server:", messageData);
    }
  });
  dgSocket.addEventListener("error", (err) => {
    console.error("WebSocket error:", err);
  });
}

/**
 * Interprets a transcript command by normalizing the transcript and
 * invoking the action for the last matching keyword.
 * @param {string} transcript - The transcript text.
 */
function interpretCommand(transcript, recordAfter) {
  console.log("Transcript:", transcript);

  const normalizedTranscript = transcript
    .toLowerCase()
    .normalize("NFD")
    .replace(/[\u0300-\u036f]/g, "")
    .replace(/[^\w\s]/g, "")
    .replace(/\s+/g, "")
    .replace("the", "").trim();

  const commands = [
    {
      triggers: ["sun", "clear", "sony", "sonny"],
      action: () => window.app.makeSunny(),
      message: "Changing weather to sunny."
    },
    {
      triggers: ["rain"],
      action: () => window.app.makeRain(),
      message: "Changing weather to rainy."
    },
    {
      triggers: ["fog"],
      action: () => window.app.makeFog(),
      message: "Changing weather to foggy."
    },
    {
      triggers: ["enableheat", "onheat", "showheat", "showmeheat", "openheat"],
      action: () => window.app.openUHI(),
      message: "Showing heat map."
    },
    {
      triggers: ["opencity", "basic", "openbasic"],
      action: () => window.app.openBasic(),
      message: "Showing basic city."
    },
    {
      triggers: ["3d", "threed", "disableheat", "offheat", "hideheat"],
      action: () => window.app.open3DCity(),
      message: "Opening City Mesh."
    },
    {
      triggers: ["day"],
      action: () => window.app.makeDay(),
      message: "Setting time to day."
    },
    {
      triggers: ["night"],
      action: () => window.app.makeNight(),
      message: "Setting time to night."
    },
    {
      triggers: ["via"],
      action: () => window.app.toggleVia(),
      message: "Setting via positions."
    },
    {
      triggers: ["grid"],
      action: () => window.app.openGrid(),
      message: "Opening grid."
    },
    {
      triggers: ["capab"],
      action: () => {
        // if (dgSocket && dgSocket.readyState === WebSocket.OPEN) {
        //   dgSocket.send(
        //     JSON.stringify({
        //       type: "chat",
        //       text: currentUserTranscript.trim(),
        //     })
        //   );
        // }
      },
      message: "I can go to places in the map. show heat map. change the weather. and set time to day, or night."
    },
    {
      triggers: ["goto"],
      // We'll define this as async so we can await the fetch call
      async action() {
        try {
          // 1) Extract the substring after "go to"
          //    e.g. if user says: "go to   the main campus"
          //    we want "the main campus"
          const phraseToFind = "go to";
          let name_query = "";
          const indexOfGoto = transcript.toLowerCase().lastIndexOf(phraseToFind);
          if (indexOfGoto >= 0) {
            // substring after "go to"
            name_query = transcript.substring(indexOfGoto + phraseToFind.length).trim();
          }
          if (!name_query) {
            console.log("No substring found after 'go to'.");
            return;
          }

          console.log("Name query extracted:", name_query);

          // 2) Send request to /api/find-similar-name 
          const response = await fetch("https://{ host }}/api/find-similar-name", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ name: name_query })
          });
          if (!response.ok) {
            console.error("Failed to query /api/find-similar-name");
            return;
          }
          const data = await response.json();
          if (!data.similar_name || !data.metadata) {
            console.error("No similar name or metadata returned by the server.");
            return;
          }

          const { similar_name, metadata } = data;
          const { latitude, longitude } = metadata;
          console.log(`Server matched '${similar_name}' with coords:`, latitude, longitude);

          // 3) Call window.app.goToCoordinate()
          window.app.goToCoordinate(latitude, longitude);

          // 4) Speak the dynamic "Going to ..." message
          const message = `Going to ${similar_name}`;
          await playTTSFromServer(message);
        } catch (error) {
          console.error("Error in goto command action:", error);
        }
      },
      // We'll leave 'message' blank because we want dynamic TTS
      message: ""
    }
  ];

  let latestMatch = { index: -1, action: null };

  for (const { triggers, action, message } of commands) {
    for (const trigger of triggers) {
      const idx = normalizedTranscript.lastIndexOf(trigger);
      if (idx > latestMatch.index) {
        latestMatch = { index: idx, trigger, action, message };
      }
    }
  }

  if (latestMatch.action) {

    stopRecording();
    RECORD_BTN.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>Connecting...</span>';

    console.log("====> Triggered:", latestMatch.trigger);
    console.log("====> Transcript:", latestMatch.trigger);
    if (latestMatch.trigger[0] != "goto") {
      playTTSFromServer(latestMatch.message);
    }
    latestMatch.action();
  } else {
    // console.log("====> Trigger Not Found.");
    // console.log("====> Transcript:", latestMatch.trigger);
    // stopRecording();
    // if (dgSocket && dgSocket.readyState === WebSocket.OPEN) {
    //   dgSocket.send(
    //     JSON.stringify({
    //       type: "chat",
    //       text: currentUserTranscript.trim(),
    //     })
    //   );
    // }
  }

  console.log("Interpreted command:", normalizedTranscript);
}

/**
 * Stops the audio recording, stops the media stream, interprets the final transcript, and finalizes the recording process.
 */
function stopRecording() {
  console.log("Stopping recording...");
  isRecording = false;

  if (interpretCommandTimer) {
    clearTimeout(interpretCommandTimer);
    interpretCommandTimer = null;
  }

  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId);
    animationFrameId = null;
  }
  if (audioContext) {
    audioContext.close().catch(console.error);
    audioContext = null;
    analyser = null;
  }

  RECORD_BTN.innerHTML = '<i class="fas fa-microphone"></i> <span>Start Recording</span>';
  if (mediaRecorder && mediaRecorder.state !== "inactive") {
    mediaRecorder.stop();
  }
  if (stream) {
    stream.getTracks().forEach((track) => track.stop());
    stream = null;
  }
}

/**
 * Visualizes audio data by scaling an icon based on audio frequency analysis.
 * Uses the Web Audio API's analyser node to get frequency data and applies
 * a visual scaling effect to a material-icons element. The visualization
 * continues through requestAnimationFrame until stopped.
 */
function visualizeAudio() {
  const dataArray = new Uint8Array(analyser.frequencyBinCount);
  analyser.getByteFrequencyData(dataArray);

  const average = dataArray.reduce((a, b) => a + b, 0) / dataArray.length;

  const iconSize = Math.max(1, Math.min(1.5, 1 + average / 128));
  const iconElement = RECORD_BTN.querySelector('.material-icons');
  if (iconElement) {
    iconElement.style.transform = `scale(${iconSize})`;
  }

  animationFrameId = requestAnimationFrame(visualizeAudio);
}

/**
 * Requests TTS from the server and plays the streamed audio.
 * @param {string} text - The text to convert to speech.
 * @returns {Promise<void>} Resolves when the audio playback ends.
 */
async function playTTSFromServer(text, recordAfter) {
  return new Promise((resolve, reject) => {
    const mediaSource = new MediaSource();
    const audioElement = new Audio();
    audioElement.src = URL.createObjectURL(mediaSource);
    currentAudioElement = audioElement;

    audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const source = audioContext.createMediaElementSource(audioElement);
    analyser = audioContext.createAnalyser();
    analyser.fftSize = 32;
    source.connect(analyser);
    analyser.connect(audioContext.destination);

    RECORD_BTN.innerHTML = '<i class="material-icons" style="font-size: 24px;">graphic_eq</i> <span>AI Speaking...</span>';
    visualizeAudio();

    audioElement.addEventListener("ended", () => {
      console.log("AI Audio Ended");
      currentAudioElement = null;
      resolve();
      if (recordAfter)
        startRecording();
      else
        stopRecording();
    });

    audioElement.addEventListener("error", (e) => {
      console.error("Audio playback error:", e);
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
        animationFrameId = null;
      }
      reject(e);
    });

    mediaSource.addEventListener("sourceopen", async () => {
      const sourceBuffer = mediaSource.addSourceBuffer("audio/mpeg");
      try {
        const response = await fetch("/api/tts", {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({ text })
        });
        if (!response.ok) {
          const errText = await response.text();
          throw new Error("TTS request failed: " + errText);
        }
        if (!response.body) {
          throw new Error("ReadableStream not supported in this browser.");
        }
        const reader = response.body.getReader();
        let isFirstAppend = true;

        sourceBuffer.addEventListener("updateend", () => {
          // Check if this was the last append and end the stream
          if (!isFirstAppend && mediaSource.readyState === "open") {
            try {
              mediaSource.endOfStream();
            } catch (e) {
              console.error("Error ending media stream:", e);
            }
          }
        });

        while (true) {
          const { value, done } = await reader.read();
          if (done) break;

          await waitForUpdateEnd(sourceBuffer);
          sourceBuffer.appendBuffer(value);

          if (isFirstAppend) {
            audioElement.play().catch((err) => console.error("Playback error:", err));
            isFirstAppend = false;
          }
        }
      } catch (error) {
        console.error("TTS playback error:", error);
        reject(error);
      }
    });
  });
}

/**
 * Returns a promise that resolves when the SourceBuffer has finished updating.
 * @param {SourceBuffer} sourceBuffer - The SourceBuffer instance.
 * @returns {Promise<void>}
 */
function waitForUpdateEnd(sourceBuffer) {
  return new Promise((resolve) => {
    if (!sourceBuffer.updating) {
      resolve();
    } else {
      sourceBuffer.addEventListener("updateend", resolve, { once: true });
    }
  });
}