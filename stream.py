import json
import asyncio
import websockets
from typing import Set


class StreamServer:
    def __init__(self, host="0.0.0.0", port=8000):
        self.host = host
        self.port = port
        self.clients: Set[websockets.WebSocketServerProtocol] = set()

    async def broadcast(self, message: str):
        """Broadcast message to all connected clients"""
        if self.clients:
            await asyncio.gather(
                *[client.send(message) for client in self.clients]
            )

    async def on_connection(self, websocket: websockets.WebSocketServerProtocol):
        """Handle new client connections"""
        self.clients.add(websocket)
        try:
            async for message in websocket:
                await self.on_message(websocket, message)
        finally:
            self.clients.remove(websocket)

    async def handshake(self, websocket: websockets.WebSocketServerProtocol, message):
        print("Handshake")
        """Handle handshake with client"""
        try:
            parsed = json.loads(message)
            if parsed.get("spatialReference", {}).get("wkid") != 102100:
                await websocket.close()
                return

            response = {
                "format": "json",
                "spatialReference": {
                    "wkid": parsed["spatialReference"]["wkid"]
                }
            }
            await websocket.send(json.dumps(response))
        except Exception as e:
            print(f"Got error on handshake: {e}")
            await websocket.close()

    async def on_message(self, websocket: websockets.WebSocketServerProtocol, message):
        """Handle incoming messages"""
        if not isinstance(message, str):
            await websocket.close()
            return
        await self.handshake(websocket, message)

    async def start(self):
        """Start the WebSocket server"""
        server = await websockets.serve(
            self.on_connection,
            self.host,
            self.port
        )
        print(f"Started listening on port {self.port}")
        return server


