import os
import csv
import json
import threading
import queue
import time
import requests
import asyncio

from flask import Flask, send_from_directory, request, Response, jsonify
from flask_sock import Sock
from flask_cors import CORS
from dotenv import load_dotenv
from openai import OpenAI
from deepgram import DeepgramClient, LiveTranscriptionEvents, LiveOptions, LiveResultResponse

from esriToken import init_token
from stream import StreamServer
import pandas as pd
import chromadb
from sentence_transformers import SentenceTransformer

# Load environment variables
load_dotenv()

app = Flask(__name__, static_folder='public', static_url_path='')

# Initialize vector db
df = pd.read_csv("places_filtered.csv") 
client = chromadb.PersistentClient(path="chromadb_data")
collection = client.get_or_create_collection("places_collection")
embedder = SentenceTransformer("all-MiniLM-L6-v2")
CORS(app)

DEEPGRAM_API_KEY = os.environ.get("DEEPGRAM_API_KEY")
UTSA_USERNAME = os.environ.get("UTSA_ARCGIS_USERNAME")
UTSA_PASSWORD = os.environ.get("UTSA_ARCGIS_PASSWORD")

dg_client = DeepgramClient(api_key=DEEPGRAM_API_KEY)
client = OpenAI(api_key=os.environ.get("OPENAI_API_KEY"))
sock = Sock(app)

for idx, row in df.iterrows():
    name = row.get("Name", "")
    address_str = str(row.get("addresses", ""))
    
    # Combine for the embedding text
    embedding_text = f"{name} {address_str}"
    embedding_vector = embedder.encode(embedding_text).tolist()

    # Store relevant metadata (including lat/lon)
    collection.add(
        embeddings=[embedding_vector],
        documents=[embedding_text],
        metadatas=[{
            "Name": name,
            "Latitude": row.get("Latitude", None),
            "Longitude": row.get("Longitude", None)
        }],
        ids=[str(idx)]
    )


@app.route('/api/find-similar-name', methods=['POST'])
def find_similar_name():
    """
    Expects JSON input with a "name" field, e.g.:
    {
      "name": "Alamo Dome"
    }

    Returns:
    {
      "similar_name": <the best-match name>,
      "metadata": {
          "latitude": ...,
          "longitude": ...
      }
    }
    or a JSON response with a 'message' explaining no match was found.
    """
    data = request.get_json()
    query_name = data.get("name", "")
    print("Searching:", query_name)
    if not query_name:
        return jsonify({"error": "Please provide a 'name' in JSON"}), 400

    # Embed the query
    query_embedding = embedder.encode(query_name).tolist()

    # Search top-1 result
    results = collection.query(
        query_embeddings=[query_embedding],
        n_results=1
    )

    # If no results (unlikely in this simple example, but just in case)
    if not results["ids"] or len(results["ids"][0]) == 0:
        return jsonify({"message": "No match found"}), 404

    # Extract the best match
    best_distance = results["distances"][0][0]
    best_metadata = results["metadatas"][0][0]  # we stored lat/lon here
    best_name = best_metadata["Name"]

    # Example distance threshold check (L2 distance)
    threshold_distance = 1.5
    if best_distance < threshold_distance:
        return jsonify({
            "similar_name": best_name,
            "metadata": {
                "latitude": best_metadata["Latitude"],
                "longitude": best_metadata["Longitude"]
            }
        })
    else:
        # No match under threshold
        return jsonify({"best_distance": best_distance, "threshold_distance": threshold_distance, "message": "No match found"}), 404

monitorGPSFlag = False
host = "utsa-ai-lab.me"
is_secured = False
# host = "localhost:3035"

@app.route('/gps')
def gps():
    global host, is_secured
    host = request.host.split(":")[0]
    with open(os.path.join(app.static_folder, 'gps.html'), 'r') as file:
        content = file.read()
    # Replace the placeholder with the actual host
    content = content.replace('{{ host }}', host)
    return Response(content, mimetype='text/html')

@app.route('/')
def index():
    global host, monitorGPSFlag, is_secured
    """
    Serve the index.html file from the public directory.

    Returns:
        Response: The content of index.html.
    """
    host = request.host.split(":")[0]
    token, username = init_token(UTSA_USERNAME, UTSA_PASSWORD, request.headers.get('X-Forwarded-Host', request.host))
    if token == None:
        return "Error generating token", 500

    # Read the index.html file
    with open(os.path.join(app.static_folder, 'index.html'), 'r') as file:
        content = file.read()

    # Replace the placeholder with the actual host
    gpsFlag = "F"
    if monitorGPSFlag:
        gpsFlag = "T"
    content = content.replace('{{ host }}', host).replace('{{ token }}', token).replace('{{ username }}', username).replace('{{ gpsFlag }}', gpsFlag)

    # Return the modified content
    return Response(content, mimetype='text/html')

@app.route('/api/tts', methods=['POST'])
def tts():
    """
    Receive JSON text, generate TTS audio via Deepgram, and stream the audio response.

    Expects:
        JSON payload with a "text" field containing the text to convert.

    Returns:
        Response: A streaming response with MIME type audio/mpeg if successful.
        dict: An error message with status code 400 or 500 on failure.
    """
    data = request.get_json()
    text = data.get("text", "").strip()
    if not text:
        return {"error": "No text provided"}, 400

    url = "https://api.deepgram.com/v1/speak?model=aura-asteria-en"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Token {DEEPGRAM_API_KEY}"
    }
    payload = json.dumps({"text": text})

    try:
        r = requests.post(url, headers=headers, data=payload, stream=True)

        if r.status_code != 200:
            err_text = r.text
            print("Deepgram TTS error:", err_text)
            return {"error": "Deepgram TTS failed", "details": err_text}, 500

        def generate_audio():
            """
            Generator that yields chunks of audio data from Deepgram's TTS response.

            Yields:
                bytes: A chunk of audio data.
            """
            for chunk in r.iter_content(chunk_size=4096):
                if chunk:
                    yield chunk

        return Response(generate_audio(), mimetype='audio/mpeg')

    except Exception as e:
        print("Error calling Deepgram TTS:", e)
        return {"error": "Server error calling Deepgram TTS"}, 500

@sock.route('/ws')
def audio_ws(ws):
    """
    Handle a WebSocket connection for audio streaming and chat interactions.

    This endpoint:
    - Receives binary audio data from the client and forwards it to Deepgram.
    - Receives transcript events from Deepgram and sends them to the client.
    - Processes chat messages by triggering OpenAI chat completions and streams responses.

    Args:
        ws: The WebSocket connection object.
    """
    conversation_history = [
        {
            "role": "system",
            "content": (
                "You are the digital twin assistant of San Antonio. "
                "Your capabilities are going to places in the map, showing heat map, changing the weather, and setting time to day or night. "
                "If the user specified a command that's within your capabilities, ask the user to repeat the command with more clarity. "
                "Otherwise, answer the user's questions with concise and short answers in a professional tone."
            )
        }
    ]
    dg_connection = None
    audio_queue = queue.Queue()
    stop_audio_thread = threading.Event()

    def audio_sender():
        """
        Continuously read audio data from the queue and forward it to Deepgram.
        """
        while not stop_audio_thread.is_set():
            try:
                chunk = audio_queue.get(timeout=0.5)
                if chunk is None:
                    break
                if dg_connection:
                    dg_connection.send(chunk)
            except queue.Empty:
                continue

    audio_thread = threading.Thread(target=audio_sender)
    audio_thread.start()

    def on_open(client, *args, **kwargs):
        """
        Callback invoked when the Deepgram connection is opened.
        """
        print("Deepgram: connected")

    def on_transcript(client, result, **kwargs):
        """
        Callback invoked when a transcript is received from Deepgram.

        Args:
            result: The transcript result received from Deepgram.
        """
        if not result.channel.alternatives:
            return
        try:
            ws.send(json.dumps({
                "type": "transcript",
                "data": result.to_dict()
            }))
        except Exception as e:
            print("Error sending transcript to client:", e)

    def on_error(client, err, *args, **kwargs):
        """
        Callback invoked when an error occurs in the Deepgram connection.

        Args:
            err: The error encountered.
        """
        print(f"Deepgram error: {err}")
        try:
            ws.send(json.dumps({"type": "error", "message": str(err)}))
        except:
            pass

    def on_close(client, *args, **kwargs):
        """
        Callback invoked when the Deepgram connection is closed.
        """
        print("Deepgram: disconnected")

    dg_connection = dg_client.listen.websocket.v('1')
    dg_connection.on(LiveTranscriptionEvents.Open, on_open)
    dg_connection.on(LiveTranscriptionEvents.Transcript, on_transcript)
    dg_connection.on(LiveTranscriptionEvents.Error, on_error)
    dg_connection.on(LiveTranscriptionEvents.Close, on_close)

    live_opts = LiveOptions(
        language='en',
        punctuate=True,
        smart_format=True,
        model='nova-3',
        interim_results=True
    )
    dg_connection.start(live_opts)

    while True:
        raw_data = None
        try:
            raw_data = ws.receive()
        except:
            break

        if raw_data is None:
            break

        if isinstance(raw_data, bytes):
            audio_queue.put(raw_data)
        else:
            try:
                msgObj = json.loads(raw_data)
                if msgObj.get("type") == "chat":
                    user_text = msgObj.get("text", "")
                    process_chat_message(ws, conversation_history, user_text)
                else:
                    print("Unknown JSON message from client:", msgObj)
            except json.JSONDecodeError:
                print("Received text data that is not valid JSON:", raw_data)

    print("Socket: client disconnected")
    try:
        dg_connection.finish()
    except:
        pass

    stop_audio_thread.set()
    audio_queue.put(None)
    audio_thread.join()

def process_chat_message(ws, conversation_history, user_text):
    """
    Process a chat message from the client and stream a response using OpenAI's chat completion.

    This function appends the user's message to the conversation history, echoes the message
    back to the client, and initiates a streaming chat completion request. Partial and final
    responses from OpenAI are sent back to the client.

    Args:
        ws: The WebSocket connection object.
        conversation_history (list): The list containing the ongoing conversation context.
        user_text (str): The user's chat message.
    """
    user_text = user_text.strip()
    if not user_text:
        return

    conversation_history.append({"role": "user", "content": user_text})
    try:
        ws.send(json.dumps({
            "type": "chatMessage",
            "sender": "user",
            "content": user_text
        }))
    except:
        pass

    try:
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=conversation_history,
            stream=True
        )
        assistant_message = ""

        for chunk in response:
            if chunk.choices[0].delta.content is not None:
                delta = chunk.choices[0].delta.content
                if delta:
                    assistant_message += delta
                    try:
                        ws.send(json.dumps({
                            "type": "chatResponse",
                            "sender": "assistant",
                            "content": assistant_message,
                            "streaming": True
                        }))
                    except:
                        pass

        conversation_history.append({"role": "assistant", "content": assistant_message})
        try:
            ws.send(json.dumps({
                "type": "chatResponse",
                "sender": "assistant",
                "content": assistant_message,
                "streaming": False
            }))
        except:
            pass

    except Exception as e:
        print("Error in OpenAI chat completion:", e)
        try:
            ws.send(json.dumps({
                "type": "error",
                "message": "Error processing chat message"
            }))
        except:
            pass

gpsLocations = {}
async def run_stream_server():
    global server
    server = StreamServer(port=8000)
    ws_server = await server.start()
    async with ws_server:
        await ws_server.serve_forever()



def start_asyncio_loop():
    try:
        asyncio.run(run_stream_server())
    except OSError as e:
        if e.errno == 10048:  # Port already in use
            print("StreamServer already running")
        else:
            print(f"StreamServer error: {e}")


import math

from pyproj import Transformer
def lon_lat_to_web_mercator(lon, lat):
    transformer = Transformer.from_crs("EPSG:4326", "EPSG:3857", always_xy=True)
    x, y = transformer.transform(lon, lat)
    return x, y
import time
# import winsound
def updateLocations(update):
    global server
    try:
        message = [{
            "attributes": {
                "OBJECTID": -9998,
                "TRACKID": -9998,
                "TIMESTAMP": 0
            },
            "geometry": {
                "x": 0,
                "y": 0
            }
        }]
        for id in list(update.keys()):
            feature = {
                "attributes": {
                    "OBJECTID": update[id][2],
                    "TRACKID": id,
                    "TIMESTAMP": update[id][3],
                    "ROUTE": update[id][4]
                },
                "geometry": {
                    "x": update[id][0],
                    "y": update[id][1]
                }
            }
            message.append(feature)
        print(f"Posting {len(message)} locations on map")

        message = json.dumps({
            "type": "featureResult",
            "features": message
        })
        asyncio.run(server.broadcast(message))
        # winsound.Beep(1000, 500)
    except Exception as e:
        print(f"Error broadcasting GPS locations {e}")

@app.route('/stream/point', methods=['POST'])
def streamUpdate():
    global server
    data = request.get_json()
    correctCoordinatesCount = 0
    update = {}
    for location in data.get("data"):
        longitude, latitude, objectid, trackid, timestamp, route = location.get("lon", -1), location.get("lat", -1), location.get("objectid", -1), location.get("trackid", -1), location.get("timestamp", -1), location.get("route", -1)
        if longitude == -1 or latitude == -1 or id == -1 or timestamp == -1 or route == -1:
            continue
        x, y = lon_lat_to_web_mercator(longitude, latitude)
        gpsLocations[trackid] = [x, y, objectid, timestamp, route]
        update[trackid] = [x, y, objectid, timestamp, route]
        correctCoordinatesCount += 1
    updateLocations(update)
    return {f"Success, there were {str(correctCoordinatesCount)} correct coordinates.": True}, 200

@app.route('/stream/monitor', methods=['POST'])
def streamMonitor():
    global monitorGPSFlag
    data = request.get_json()
    monitorGPSFlag = data.get("flag")
    return {f"Success, flag updated to {monitorGPSFlag}.": True}, 200

@app.route('/stream/point/delete', methods=['POST'])
def streamDeletePoint():
    global server
    data = request.get_json()
    # Now using longitude (x) and latitude (y) instead of Web Mercator coordinates
    notFoundCount = 0
    for id in data.get("data"):
        if id in gpsLocations:
            gpsLocations[id][3] = -9999 # Sets time to expired
        else:
            notFoundCount += 1
    return {f"Success and {str(notFoundCount)} were not found.": True}, 200

import requests
import google.protobuf
from google.transit import gtfs_realtime_pb2
import pandas as pd
from datetime import datetime
import time
import json


def parse_vehicle_positions(source, is_url=False):
    # Read the binary protobuf file
    feed = gtfs_realtime_pb2.FeedMessage()

    if is_url:
        response = requests.get(source)
        feed.ParseFromString(response.content)
    else:
        with open(source, 'rb') as f:
            feed.ParseFromString(f.read())

    # Print feed timestamp info
    feed_timestamp = feed.header.timestamp
    print(f"Feed timestamp: {feed_timestamp} (Unix epoch seconds)")
    print(f"Feed time: {datetime.fromtimestamp(feed_timestamp)}")

    # Extract vehicle position data
    vehicles_data = []

    for entity in feed.entity:
        if entity.HasField('vehicle'):
            vehicle = entity.vehicle

            # Create a dictionary for this vehicle
            vehicle_data = {
                'id': entity.id,
                'trip_id': vehicle.trip.trip_id if vehicle.HasField('trip') else None,
                'route_id': vehicle.trip.route_id if vehicle.HasField('trip') else None,
                'latitude': vehicle.position.latitude if vehicle.HasField('position') else None,
                'longitude': vehicle.position.longitude if vehicle.HasField('position') else None,
                'bearing': vehicle.position.bearing if vehicle.HasField('position') else None,
                'speed': vehicle.position.speed if vehicle.HasField('position') else None,
                'current_stop_sequence': vehicle.current_stop_sequence if vehicle.HasField(
                    'current_stop_sequence') else None,
                'current_status': vehicle.current_status if vehicle.HasField('current_status') else None,
                'timestamp': vehicle.timestamp if vehicle.HasField('timestamp') else None,
                'congestion_level': vehicle.congestion_level if vehicle.HasField('congestion_level') else None,
                'occupancy_status': vehicle.occupancy_status if vehicle.HasField('occupancy_status') else None
            }

            vehicles_data.append(vehicle_data)

    # Create a pandas DataFrame for easier viewing and analysis
    df = pd.DataFrame(vehicles_data)

    return df


def post_vehicle_data_to_server(vehicles_df, endpoint_url):
    """
    Post vehicle data to specified endpoint

    Parameters:
    vehicles_df (pandas.DataFrame): DataFrame containing vehicle data
    endpoint_url (str): URL to post data to

    Returns:
    list: List of response objects
    """
    responses = []
    print(f"Total {len(vehicles_df)} vehicles...")
    data = {
        "data": []
    }
    missingBuses = 0
    # Loop through each vehicle and post its data
    for index, vehicle in vehicles_df.iterrows():
        # Skip vehicles with missing data
        if pd.isna(vehicle['latitude']) or pd.isna(vehicle['longitude']) or pd.isna(vehicle['trip_id']):
            # print(f"Skipping vehicle {vehicle['id']} due to missing data")
            missingBuses += 1
            continue

        # Create the data payload
        data["data"].append({
            "lat": float(vehicle['latitude']),
            "lon": float(vehicle['longitude']),
            "trackid": str(vehicle['id']),
            "objectid": str(vehicle['id']) + str(int(vehicle['timestamp'])),
            "timestamp": int(vehicle['timestamp']),
            "route": str(vehicle['route_id'])
        })
    # if index % 25 == 0:
    print(f"Will POST {len(vehicles_df) - missingBuses} vehicles to {endpoint_url}...")
    try:
        # Make the POST request
        response = requests.post(
            endpoint_url,
            headers={'Content-Type': 'application/json'},
            data=json.dumps(data),
            timeout=5  # Add timeout to prevent hanging
        )
        if response.status_code == 200:
            print(f"Succeeded POST: {response.text}")
        else:
            print(f"Failed POST...")
    except Exception as e:
        print(f"Error posting vehicles: {str(e)}")
    data["data"] = []
    time.sleep(3)
    print(responses)

def monitorGPS():
    global monitorGPSFlag
    while True:
        if monitorGPSFlag:
            try:
                # URL to the GTFS-realtime feed
                url = "http://gtfs.viainfo.net/vehicle/vehiclepositions.pb"
                # Parse the vehicle positions from the feed
                df = parse_vehicle_positions(url, is_url=True)
                # Post the vehicle data to the server
                post_vehicle_data_to_server(df, f"https://{host}/stream/point")
            except Exception as e:
                print(f"Error monitoring GPS: {e}")
        time.sleep(10)

if __name__ == "__main__":
    # Start asyncio in a separate thread
    asyncio_loop = asyncio.new_event_loop()
    asyncio.set_event_loop(asyncio_loop)
    asyncio_thread = threading.Thread(target=start_asyncio_loop)
    asyncio_thread.daemon = True
    asyncio_thread.start()

    asyncio_loop = asyncio.new_event_loop()
    asyncio.set_event_loop(asyncio_loop)
    asyncio_thread = threading.Thread(target=monitorGPS)
    asyncio_thread.daemon = True
    asyncio_thread.start()

    # Run Flask in the main thread
    app.run(host='0.0.0.0', port=3035, debug=False)  # Set debug=False when using threads